<template>
  <div class="excel-preview">
    <table border='0' cellpadding='0' cellspacing='0' style='border-collapse: collapse;table-layout:fixed;width:432pt' width='576'>
      <col style='mso-width-source:userset;width:45.75pt' width='61'>
      <col style='mso-width-source:userset;width:82.5pt' width='110'>
      <col style='mso-width-source:userset;width:56.25pt' width='75'>
      <col style='mso-width-source:userset;width:66pt' width='88'>
      <col style='mso-width-source:userset;width:99pt' width='132'>
      <col style='mso-width-source:userset;width:82.5pt' width='110'>
      
      <!-- 第一行：致 -->
      <tr height='17' style='mso-height-source:userset;height:12.75pt'>
        <td class='x69' colspan='6' height='17' style='height:12.75pt;' width='576'>致：xxxxxxx</td>
      </tr>
      
      <!-- 第二行：结算数据说明 -->
      <tr height='20' style='mso-height-source:userset;height:15.5pt'>
        <td class='x70' colspan='6' height='19' style='height:14.75pt;'>{{ rangeTimeText }}结算数据如下，请给予确认，谢谢。</td>
      </tr>
      
      <!-- 第三行：电费计算表标题 -->
      <tr height='25' style='mso-height-source:userset;height:18.75pt'>
        <td class='x71' colspan='6' height='23' style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:17.25pt;'>
          {{ stationTitle }}电费计算表
        </td>
      </tr>
      
      <!-- 表头 -->
      <tr height='28' style='mso-height-source:userset;height:21pt'>
        <td class='x73' height='26' style='height:19.5pt;'><span class="font5">序号</span></td>
        <td class='x73' colspan='2' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'><span class="font5">时间段</span></td>
        <td class='x73'><span class="font5">电量</span></td>
        <td class='x73'><span class="font5">电价<br></span><span class="font5">(元/千瓦时)</span></td>
        <td class='x73'><span class="font5">电费合计(元)</span></td>
      </tr>
      
      <!-- 数据行 -->
      <tr height='24' style='mso-height-source:userset;height:18.5pt'>
        <td class='x74' height='22' style='height:17pt;'>1</td>
        <td class='x75' colspan='2' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>{{ dateRange }}</td>
        <td class='x76'>{{ electricityKwh }}</td>
        <td class='x75'>{{ electricityPriceYuan }}</td>
        <td class='x76'>{{ electricityFee }}</td>
      </tr>
      
      <!-- 合计行 -->
      <tr height='24' style='mso-height-source:userset;height:18pt'>
        <td class='x74' height='22' style='height:16.5pt;'>2</td>
        <td class='x73' colspan='2' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'><span class="font5">合计</span></td>
        <td class='x76'>{{ electricityKwh }}</td>
        <td class='x77'></td>
        <td class='x76'>{{ electricityFee }}</td>
      </tr>
      
      <!-- 说明标题 -->
      <tr height='24' style='mso-height-source:userset;height:18.5pt'>
        <td class='x78' colspan='6' height='22' style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:17pt;'>
          <span class="font5">说明：</span>
        </td>
      </tr>
      
      <!-- 说明内容 -->
      <tr height='48' style='mso-height-source:userset;height:36.25pt'>
        <td class='x80' colspan='6' height='46' style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:34.75pt;'>
          {{ electricityExplanation }}
        </td>
      </tr>
      
      <!-- 空行 -->
      <tr height='6' style='mso-height-source:userset;height:5pt'>
        <td colspan='6' height='6' style='height:5pt;'></td>
      </tr>
      
      <!-- 效益分成计算表标题 -->
      <tr height='45' style='mso-height-source:userset;height:33.75pt'>
        <td class='x81' colspan='6' height='43' style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:32.25pt;'>
          {{ stationTitle }}效益分成计算表
        </td>
      </tr>
      
      <!-- 效益分成表头 -->
      <tr height='39' style='mso-height-source:userset;height:29.5pt'>
        <td class='x73' height='37' style='height:28pt;'><span class="font5">序号</span></td>
        <td class='x73'><span class="font5">结算账单期间</span></td>
        <td class='x82'><span class="font5">期间服务费<br></span><span class="font5">(元)</span></td>
        <td class='x73'><span class="font5">合同比例</span></td>
        <td class='x83'>新能公司{{ serviceRatio }}%效益分成<br>(元)</td>
        <td class='x83'>xxxx{{ otherServiceRatio }}%效益分成(元)</td>
      </tr>
      
      <!-- 效益分成数据行 -->
      <tr height='29' style='mso-height-source:userset;height:22pt'>
        <td class='x74' height='27' style='height:20.5pt;'>1</td>
        <td class='x75'>{{ dateRange }}</td>
        <td class='x76'>{{ servicePriceYuan }}</td>
        <td class='x84'>{{ otherServiceRatio }}%</td>
        <td class='x85'>{{ newEnergyShare }}</td>
        <td class='x85'>{{ otherCompanyShare }}</td>
      </tr>
      
      <!-- 效益分成合计行 -->
      <tr height='28' style='mso-height-source:userset;height:21.5pt'>
        <td class='x74' height='26' style='height:20pt;'>2</td>
        <td class='x73'><span class="font5">合计</span></td>
        <td class='x86'>{{ servicePriceYuan }}</td>
        <td class='x87' colspan='2' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>
          xxxx效益分成合计(元)
        </td>
        <td class='x85'>{{ otherCompanyShare }}</td>
      </tr>
      
      <!-- 效益分成说明 -->
      <tr height='52' style='mso-height-source:userset;height:39pt'>
        <td class='x73' height='50' style='height:37.5pt;'><span class="font5">说明</span></td>
        <td class='x88' colspan='5' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>
          {{ serviceExplanation }}
        </td>
      </tr>
      
      <!-- 空行 -->
      <tr height='6' style='mso-height-source:userset;height:5pt'>
        <td height='6' style='height:5pt;'></td>
        <td colspan='5'></td>
      </tr>
      
      <!-- 应付款统计表标题 -->
      <tr height='25' style='mso-height-source:userset;height:19.25pt'>
        <td class='x81' colspan='6' height='23' style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:17.75pt;'>
          {{ stationTitle }} 应 付 款 统 计 表
        </td>
      </tr>
      
      <!-- 应付款表头 -->
      <tr height='32' style='mso-height-source:userset;height:24pt'>
        <td class='x89' height='30' style='height:22.5pt;'><span class="font9">序号</span></td>
        <td class='x89'><span class="font9">项 目</span></td>
        <td class='x90'><span class="font9">金额<br></span><span class="font9">(元)</span></td>
        <td class='x89' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'><span class="font9">备注</span></td>
      </tr>
      
      <!-- 电费应付款 -->
      <tr height='25' style='mso-height-source:userset;height:19pt'>
        <td class='x91' height='23' style='height:17.5pt;'>1</td>
        <td class='x89'><span class="font9">电费应付款</span></td>
        <td class='x92'>{{ electricityFee }}</td>
        <td class='x93' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'></td>
      </tr>
      
      <!-- 效益分成应付款 -->
      <tr height='26' style='mso-height-source:userset;height:19.5pt'>
        <td class='x91' height='24' style='height:18pt;'>2</td>
        <td class='x89'><span class="font9">效益分成应付款</span></td>
        <td class='x92'>{{ otherCompanyShare }}</td>
        <td class='x93' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>
          按合同比例计算服务费分成
        </td>
      </tr>
      
      <!-- 期间应付款合计 -->
      <tr height='25' style='mso-height-source:userset;height:19pt'>
        <td class='x91' height='23' style='height:17.5pt;'>3</td>
        <td class='x89'><span class="font9">期间应付款合计</span></td>
        <td class='x94'>{{ totalPayable }}</td>
        <td class='x89' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>
          期间合计应付款项
        </td>
      </tr>
      
      <!-- 本期应付款 -->
      <tr height='26' style='mso-height-source:userset;height:19.5pt'>
        <td class='x91' height='24' style='height:18pt;'>4</td>
        <td class='x89'><span class="font9">本期应付款</span></td>
        <td class='x94'>{{ totalPayable }}</td>
        <td class='x77' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'></td>
      </tr>
      
      <!-- 收款信息 -->
      <tr height='65' style='mso-height-source:userset;height:49pt'>
        <td class='x95' colspan='6' height='63' style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:47.5pt;'>
          说明：<br>收款账户：xxxxxxxx<br>开户行：中国银行深圳中兴支行<br>账号：754957950278
        </td>
      </tr>
      
      <!-- 公司名称 -->
      <tr height='18' style='mso-height-source:userset;height:13.5pt'>
        <td class='x96' colspan='6' height='17' style='height:12.75pt;'>
          <span style='float:right'><span style='mso-spacerun:yes;'>&nbsp; </span>深圳新能智慧充电科技有限公司</span>
        </td>
      </tr>
      
      <!-- 日期 -->
      <tr height='18' style='mso-height-source:userset;height:13.5pt'>
        <td colspan='4' height='18' style='height:13.5pt;'></td>
        <td></td>
        <td class='x98'><span style='float:right'>日期：{{ currentDate }}</span></td>
      </tr>
    </table>
  </div>
</template>

<script>
export default {
  name: 'ExcelPreview',
  props: {
    // 场站数据
    stationData: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  computed: {
    // 统计时间文本
    rangeTimeText() {
      return this.stationData.range_time || '2025年1月-3月';
    },
    
    // 场站标题
    stationTitle() {
      const stationName = this.stationData.station_name || '英伦名苑二期充电站';
      const rangeTime = this.stationData.range_time || '2025年1月-3月';
      return `${stationName}${rangeTime}`;
    },
    
    // 日期范围（格式化为25.01-25.03）
    dateRange() {
      return this.formatYearRange(this.stationData.range_time || '2025年1月-3月');
    },
    
    // 电量（千瓦时）
    electricityKwh() {
      const electricity = this.stationData.electricity_total || 0;
      return (electricity / 10000).toFixed(2);
    },
    
    // 电价（元/千瓦时）
    electricityPriceYuan() {
      const price = this.stationData.electricity_price || 0;
      return (price / 10000).toFixed(2);
    },
    
    // 电费（元）
    electricityFee() {
      const electricity = this.stationData.electricity_total || 0;
      const price = this.stationData.electricity_price || 0;
      return ((electricity / 10000) * (price / 10000)).toFixed(2);
    },
    
    // 服务费分成比例
    serviceRatio() {
      return this.stationData.ratio_ser_price || 80;
    },
    
    // 其他公司分成比例
    otherServiceRatio() {
      return 100 - this.serviceRatio;
    },
    
    // 服务费（元）
    servicePriceYuan() {
      const servicePrice = this.stationData.service_price || 0;
      return (servicePrice / 10000).toFixed(2);
    },
    
    // 新能公司分成
    newEnergyShare() {
      const servicePrice = this.stationData.service_price || 0;
      return ((servicePrice / 10000) * (this.serviceRatio / 100)).toFixed(2);
    },
    
    // 其他公司分成
    otherCompanyShare() {
      const servicePrice = this.stationData.service_price || 0;
      return ((servicePrice / 10000) * (this.otherServiceRatio / 100)).toFixed(2);
    },
    
    // 总应付款
    totalPayable() {
      const electricityFee = parseFloat(this.electricityFee);
      const otherShare = parseFloat(this.otherCompanyShare);
      return (electricityFee + otherShare).toFixed(2);
    },
    
    // 电费说明
    electricityExplanation() {
      const rangeTime = this.stationData.range_time || '2025年1月-3月';
      return `1、电费计算周期：${rangeTime}\n2、数据取自电表：上次读数xxx；本次读数xxx；倍率xxx；实用量xxxx`;
    },
    
    // 服务费说明
    serviceExplanation() {
      const rangeTime = this.stationData.range_time || '2025年1月-3月';
      const monthRange = this.parseMonthRange(rangeTime);
      const startDate = monthRange.start;
      const endDate = monthRange.end;
      return `1. 服务费计算周期：${rangeTime}\n2. 服务费根据${startDate}-${endDate}订单中服务费的总额-巡检订单总服务费`;
    },
    
    // 当前日期
    currentDate() {
      const now = new Date();
      return `${now.getFullYear()}.${now.getMonth() + 1}.${now.getDate()}`;
    }
  },
  methods: {
    // 格式化年份范围（如：2025年1月-3月 -> 25.01-25.03）
    formatYearRange(dateRange) {
      if (!dateRange) return '';
      
      try {
        // 匹配两种格式：跨年格式（2024年12月-2025年3月）和同年度格式（2025年1月-3月）
        let matches = dateRange.match(/(\d{4})年(\d{1,2})月-(\d{4})年(\d{1,2})月/);
        if (matches) {
          // 跨年格式处理
          const startYear = matches[1];
          const startMonth = matches[2];
          const endYear = matches[3];
          const endMonth = matches[4];
          return `${startYear.slice(-2)}.${startMonth.padStart(2, '0')}-${endYear.slice(-2)}.${endMonth.padStart(2, '0')}`;
        }
        
        matches = dateRange.match(/(\d{4})年(\d{1,2})月-(\d{1,2})月/);
        if (matches) {
          // 同年度格式处理
          const year = matches[1];
          const startMonth = matches[2];
          const endMonth = matches[3];
          return `${year.slice(-2)}.${startMonth.padStart(2, '0')}-${year.slice(-2)}.${endMonth.padStart(2, '0')}`;
        }
        
        return dateRange;
      } catch (error) {
        console.error('日期格式化错误:', error);
        return dateRange;
      }
    },
    
    // 解析月份范围
    parseMonthRange(monthRange) {
      if (!monthRange) return { start: '', end: '' };
      
      try {
        // 匹配两种格式：跨年格式（2024年12月-2025年3月）和同年度格式（2025年1月-3月）
        let matches = monthRange.match(/(\d{4})年(\d{1,2})月-(\d{4})年(\d{1,2})月/);
        if (matches) {
          // 跨年格式处理
          const startYear = matches[1];
          const startMonth = matches[2];
          const endYear = matches[3];
          const endMonth = matches[4];
          return {
            start: `${startYear}年${startMonth}月1日`,
            end: `${endYear}年${endMonth}月31日`
          };
        }
        
        matches = monthRange.match(/(\d{4})年(\d{1,2})月-(\d{1,2})月/);
        if (matches) {
          // 同年度格式处理
          const year = matches[1];
          const startMonth = matches[2];
          const endMonth = matches[3];
          return {
            start: `${year}年${startMonth}月1日`,
            end: `${year}年${endMonth}月31日`
          };
        }
        
        return { start: monthRange, end: monthRange };
      } catch (error) {
        console.error('月份范围解析错误:', error);
        return { start: monthRange, end: monthRange };
      }
    }
  }
};
</script>
