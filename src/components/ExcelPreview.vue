<template>
  <div class="excel-preview">
    <table style='border-collapse: collapse;table-layout:fixed;width:432pt'>
      <tr height='17' id='r0' style='mso-height-source:userset;height:12.75pt'>
        <td class='x69' colspan='6' height='17' style='height:12.75pt;'>致：{{ toCompany }}</td>
      </tr>
      <tr height='20' id='r1' style='mso-height-source:userset;height:15.5pt'>
        <td class='x70' colspan='6' height='19' style='height:14.75pt;'>{{ rangeTimeText }}结算数据如下，请给予确认，谢谢。</td>
      </tr>
      <tr height='25' id='r2' style='mso-height-source:userset;height:18.75pt'>
        <td class='x71' colspan='6' height='23'
            style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:17.25pt;'>
          {{ stationTitle }}电费计算表
        </td>
      </tr>
      <tr height='28' id='r3' style='mso-height-source:userset;height:21pt'>
        <td class='x73' height='26' style='height:19.5pt;'><span class="font5">序号</span></td>
        <td class='x73' colspan='2' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'><span
                class="font5">时间段</span></td>
        <td class='x73'><span class="font5">电量</span></td>
        <td class='x73'><span class="font5">电价<br></span><span class="font5">(元/千瓦时)</span></td>
        <td class='x73'><span class="font5">电费合计(元)</span></td>
      </tr>
      <tr height='24' id='r4' style='mso-height-source:userset;height:18.5pt'>
        <td class='x74' height='22' style='height:17pt;'>1</td>
        <td class='x75' colspan='2' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>{{ dateRange }}</td>
        <td class='x76'>{{ electricityKwh }}</td>
        <td class='x75'>{{ electricityPriceYuan }}</td>
        <td class='x76'>{{ electricityFee }}</td>
      </tr>
      <tr height='24' id='r5' style='mso-height-source:userset;height:18pt'>
        <td class='x74' height='22' style='height:16.5pt;'>2</td>
        <td class='x73' colspan='2' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'><span
                class="font5">合计</span></td>
        <td class='x76'>{{ electricityKwh }}</td>
        <td class='x77'></td>
        <td class='x76'>{{ electricityFee }}</td>
      </tr>
      <tr height='24' id='r6' style='mso-height-source:userset;height:18.5pt'>
        <td class='x78' colspan='6' height='22'
            style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:17pt;'><span
                class="font5">说明：</span></td>
      </tr>
      <tr height='48' id='r7' style='mso-height-source:userset;height:36.25pt'>
        <td class='x80' colspan='6' height='46'
            style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:34.75pt;'>
          1、电费计算周期：{{ rangeTimeText }}<br>
          2、数据取自电表：上次读数xxx；本次读数xxx；倍率xxx；实用量xxxx
        </td>
      </tr>
      <tr height='6' id='r8' style='mso-height-source:userset;height:5pt'>
        <td colspan='6' height='6' style='height:5pt;'></td>
      </tr>
      <tr height='45' id='r9' style='mso-height-source:userset;height:33.75pt'>
        <td class='x81' colspan='6' height='43'
            style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:32.25pt;'>
          {{ stationTitle }}效益分成计算表
        </td>
      </tr>
      <tr height='39' id='r10' style='mso-height-source:userset;height:29.5pt'>
        <td class='x73' height='37' style='height:28pt;'><span class="font5">序号</span></td>
        <td class='x73'><span class="font5">结算账单期间</span></td>
        <td class='x82'><span class="font5">期间服务费<br></span><span class="font5">(元)</span></td>
        <td class='x73'><span class="font5">合同比例</span></td>
        <td class='x83'>新能公司{{ serviceRatio }}%效益分成<br>(元)</td>
        <td class='x83'>xxxx{{ otherServiceRatio }}%效益分成(元)</td>
      </tr>
      <tr height='29' id='r11' style='mso-height-source:userset;height:22pt'>
        <td class='x74' height='27' style='height:20.5pt;'>1</td>
        <td class='x75'>{{ dateRange }}</td>
        <td class='x76'>{{ servicePriceYuan }}</td>
        <td class='x84'>{{ otherServiceRatio }}%</td>
        <td class='x85'>{{ newEnergyShare }}</td>
        <td class='x85'>{{ otherCompanyShare }}</td>
      </tr>
      <tr height='28' id='r12' style='mso-height-source:userset;height:21.5pt'>
        <td class='x74' height='26' style='height:20pt;'>2</td>
        <td class='x73'><span class="font5">合计</span></td>
        <td class='x86'>{{ servicePriceYuan }}</td>
        <td class='x87' colspan='2' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>
          xxxx效益分成合计(元)
        </td>
        <td class='x85'>{{ otherCompanyShare }}</td>
      </tr>
      <tr height='52' id='r13' style='mso-height-source:userset;height:39pt'>
        <td class='x73' height='50' style='height:37.5pt;'><span class="font5">说明</span></td>
        <td class='x88' colspan='5' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>
          1. 服务费计算周期：{{ rangeTimeText }}<br>
          2. 服务费根据{{ startDateText }}-{{ endDateText }}订单中服务费的总额-巡检订单总服务费
        </td>
      </tr>
      <tr height='6' id='r14' style='mso-height-source:userset;height:5pt'>
        <td height='6' style='height:5pt;'></td>
        <td colspan='5'></td>
      </tr>
      <tr height='25' id='r15' style='mso-height-source:userset;height:19.25pt'>
        <td class='x81' colspan='6' height='23'
            style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:17.75pt;'>
          {{ stationTitle }} 应 付 款 统 计 表
        </td>
      </tr>
      <tr height='32' id='r16' style='mso-height-source:userset;height:24pt'>
        <td class='x89' height='30' style='height:22.5pt;'><span class="font9">序号</span></td>
        <td class='x89'><span class="font9">项 目</span></td>
        <td class='x90'><span class="font9">金额<br></span><span class="font9">(元)</span></td>
        <td class='x89' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'><span
                class="font9">备注</span></td>
      </tr>
      <tr height='25' id='r17' style='mso-height-source:userset;height:19pt'>
        <td class='x91' height='23' style='height:17.5pt;'>1</td>
        <td class='x89'><span class="font9">电费应付款</span></td>
        <td class='x92'>{{ electricityFee }}</td>
        <td class='x93' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'></td>
      </tr>
      <tr height='26' id='r18' style='mso-height-source:userset;height:19.5pt'>
        <td class='x91' height='24' style='height:18pt;'>2</td>
        <td class='x89'><span class="font9">效益分成应付款</span></td>
        <td class='x92'>{{ otherCompanyShare }}</td>
        <td class='x93' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>
          按合同比例计算服务费分成
        </td>
      </tr>
      <tr height='25' id='r19' style='mso-height-source:userset;height:19pt'>
        <td class='x91' height='23' style='height:17.5pt;'>3</td>
        <td class='x89'><span class="font9">期间应付款合计</span></td>
        <td class='x94'>{{ totalPayable }}</td>
        <td class='x89' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>
          期间合计应付款项
        </td>
      </tr>
      <tr height='26' id='r20' style='mso-height-source:userset;height:19.5pt'>
        <td class='x91' height='24' style='height:18pt;'>4</td>
        <td class='x89'><span class="font9">本期应付款</span></td>
        <td class='x94'>{{ totalPayable }}</td>
        <td class='x77' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'></td>
      </tr>
      <tr height='65' id='r21' style='mso-height-source:userset;height:49pt'>
        <td class='x95' colspan='6' height='63'
            style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:47.5pt;'>
          说明：<br>收款账户：xxxxxxxx<br>开户行：中国银行深圳中兴支行<br>账号：754957950278
        </td>
      </tr>
      <tr height='18' id='r22' style='mso-height-source:userset;height:13.5pt'>
        <td class='x96' colspan='6' height='17' style='height:12.75pt;'>
          <span style='float:right'>
            <span style='mso-spacerun:yes;'>&nbsp; </span>{{ companyName }}
          </span>
        </td>
      </tr>
      <tr height='18' id='r23' style='mso-height-source:userset;height:13.5pt'>
        <td colspan='4' height='18' style='height:13.5pt;'></td>
        <td></td>
        <td class='x98'><span style='float:right'>日期：{{ currentDate }}</span></td>
      </tr>
    </table>
  </div>
</template>

<script>
export default {
  name: 'ExcelPreview',
  props: {
    // 场站数据
    stationData: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  computed: {
    // 对方公司名称
    toCompany() {
      return 'xxxxxxx';
    },
    
    // 公司名称
    companyName() {
      return '深圳新能智慧充电科技有限公司';
    },
    
    // 当前日期
    currentDate() {
      const now = new Date();
      return `${now.getFullYear()}.${now.getMonth() + 1}.${now.getDate()}`;
    },
    
    // 时间范围文本
    rangeTimeText() {
      return this.stationData.range_time || '';
    },
    
    // 场站标题
    stationTitle() {
      return `${this.stationData.station_name || ''}充电站${this.rangeTimeText}`;
    },
    
    // 格式化日期范围 (25.01-25.03格式)
    dateRange() {
      return this.formatYearRange(this.rangeTimeText);
    },
    
    // 电量 (千瓦时)
    electricityKwh() {
      const electricity = (this.stationData.electricity_total || 0) / 10000;
      return this.formatNumber(electricity, 2);
    },
    
    // 电价 (元/千瓦时)
    electricityPriceYuan() {
      const price = (this.stationData.electricity_price || 0) / 10000;
      return this.formatNumber(price, 2);
    },
    
    // 电费 (元)
    electricityFee() {
      const electricity = (this.stationData.electricity_total || 0) / 10000;
      const price = (this.stationData.electricity_price || 0) / 10000;
      return this.formatNumber(electricity * price, 2);
    },
    
    // 服务费分成比例
    serviceRatio() {
      return this.stationData.ratio_ser_price || 80;
    },
    
    // 其他公司分成比例
    otherServiceRatio() {
      return 100 - this.serviceRatio;
    },
    
    // 服务费 (元)
    servicePriceYuan() {
      const servicePrice = (this.stationData.service_price || 0) / 10000;
      return this.formatNumber(servicePrice, 2);
    },
    
    // 新能公司分成 (元)
    newEnergyShare() {
      const servicePrice = (this.stationData.service_price || 0) / 10000;
      const share = servicePrice * (this.serviceRatio / 100);
      return this.formatNumber(share, 2);
    },
    
    // 其他公司分成 (元)
    otherCompanyShare() {
      const servicePrice = (this.stationData.service_price || 0) / 10000;
      const share = servicePrice * (this.otherServiceRatio / 100);
      return this.formatNumber(share, 2);
    },
    
    // 应付款合计
    totalPayable() {
      const electricityFee = parseFloat(this.electricityFee) || 0;
      const otherShare = parseFloat(this.otherCompanyShare) || 0;
      return this.formatNumber(electricityFee + otherShare, 2);
    },
    
    // 开始日期文本
    startDateText() {
      const monthRange = this.parseMonthRange(this.rangeTimeText);
      if (monthRange && monthRange.start) {
        return monthRange.start.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      }
      return '';
    },
    
    // 结束日期文本
    endDateText() {
      const monthRange = this.parseMonthRange(this.rangeTimeText);
      if (monthRange && monthRange.end) {
        return monthRange.end.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      }
      return '';
    }
  },
  methods: {
    // 格式化数字
    formatNumber(num, decimals = 2) {
      if (isNaN(num)) return '0.00';
      return Number(num).toFixed(decimals);
    },
    
    // 格式化年份范围 (如: 25.01-25.03)
    formatYearRange(dateRange) {
      if (!dateRange) return '';
      
      try {
        // 匹配两种格式：跨年格式（2024年12月-2025年3月）和同年度格式（2025年1月-3月）
        let matches = dateRange.match(/(\d{4})年(\d{1,2})月-(\d{4})年(\d{1,2})月/);
        if (matches) {
          // 跨年格式处理
          const startYear = parseInt(matches[1]);
          const startMonth = parseInt(matches[2]);
          const endYear = parseInt(matches[3]);
          const endMonth = parseInt(matches[4]);
          
          return `${String(startYear).slice(-2)}.${String(startMonth).padStart(2, '0')}-${String(endYear).slice(-2)}.${String(endMonth).padStart(2, '0')}`;
        }
        
        matches = dateRange.match(/(\d{4})年(\d{1,2})月-(\d{1,2})月/);
        if (matches) {
          // 同年度格式处理
          const year = parseInt(matches[1]);
          const startMonth = parseInt(matches[2]);
          const endMonth = parseInt(matches[3]);
          
          return `${String(year).slice(-2)}.${String(startMonth).padStart(2, '0')}-${String(year).slice(-2)}.${String(endMonth).padStart(2, '0')}`;
        }
      } catch (error) {
        console.error('日期范围格式错误:', dateRange, error);
      }
      
      return dateRange;
    },
    
    // 解析月份范围
    parseMonthRange(monthRange) {
      if (!monthRange) return null;
      
      try {
        // 匹配两种格式：跨年格式（2024年12月-2025年3月）和同年度格式（2025年1月-3月）
        let matches = monthRange.match(/(\d{4})年(\d{1,2})月-(\d{4})年(\d{1,2})月/);
        if (matches) {
          // 跨年格式处理
          const startYear = parseInt(matches[1]);
          const startMonth = parseInt(matches[2]);
          const endYear = parseInt(matches[3]);
          const endMonth = parseInt(matches[4]);
          
          const startDate = new Date(startYear, startMonth - 1, 1);
          const endDate = new Date(endYear, endMonth, 0); // 当月最后一天
          
          return { start: startDate, end: endDate };
        }
        
        matches = monthRange.match(/(\d{4})年(\d{1,2})月-(\d{1,2})月/);
        if (matches) {
          // 同年度格式处理
          const year = parseInt(matches[1]);
          const startMonth = parseInt(matches[2]);
          const endMonth = parseInt(matches[3]);
          
          const startDate = new Date(year, startMonth - 1, 1);
          const endDate = new Date(year, endMonth, 0); // 当月最后一天
          
          return { start: startDate, end: endDate };
        }
      } catch (error) {
        console.error('解析月份范围错误:', monthRange, error);
      }
      
      return null;
    }
  }
};
</script>

<style scoped>
/* 保持原有的Excel样式 */
.excel-preview {
  font-family: Arial, sans-serif;
  font-size: 11pt;
}

table {
  mso-displayed-decimal-separator: "\.";
  mso-displayed-thousand-separator: "\,";
}

tr {
  mso-height-source: auto;
  mso-ruby-visibility: none;
}

col {
  mso-width-source: auto;
  mso-ruby-visibility: none;
}

br {
  mso-data-placement: same-cell;
}

td {
  mso-style-parent: style0;
  mso-number-format: General;
  text-align: left;
  vertical-align: top;
  white-space: normal;
  word-wrap: break-word;
  background: white;
  mso-pattern: auto;
  color: #000000;
  font-size: 11pt;
  font-weight: 400;
  font-style: normal;
  font-family: "Arial", "sans-serif";
  border: none;
  mso-protection: locked visible;
  mso-ignore: padding;
}

.x69 {
  mso-style-parent: style0;
  mso-number-format: General;
  text-align: left;
  vertical-align: top;
  white-space: normal;
  word-wrap: break-word;
  background: white;
  mso-pattern: auto;
  font-size: 10pt;
  font-weight: 700;
  font-style: normal;
  font-family: "SimHei", "sans-serif";
  mso-protection: locked visible;
}

.x70 {
  mso-style-parent: style0;
  mso-number-format: General;
  text-align: left;
  vertical-align: top;
  white-space: normal;
  word-wrap: break-word;
  mso-char-indent-count: 1;
  padding-left: 7px;
  background: white;
  mso-pattern: auto;
  font-size: 10pt;
  font-weight: 700;
  font-style: normal;
  font-family: "SimHei", "sans-serif";
  mso-protection: locked visible;
}
</style>
