<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
        <!--
        table {
            mso-displayed-decimal-separator: "\.";
            mso-displayed-thousand-separator: "\,";
        }

        tr {
            mso-height-source: auto;
            mso-ruby-visibility: none;
        }

        col {
            mso-width-source: auto;
            mso-ruby-visibility: none;
        }

        br {
            mso-data-placement: same-cell;
        }

        td {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: left;
            vertical-align: top;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 11pt;
            font-weight: 400;
            font-style: normal;
            font-family: "Arial", "sans-serif";
            border: none;
            mso-protection: locked visible;
            mso-ignore: padding;
        }

        .x69 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: left;
            vertical-align: top;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            font-size: 10pt;
            font-weight: 700;
            font-style: normal;
            font-family: "SimHei", "sans-serif";
            mso-protection: locked visible;
        }

        .x70 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: left;
            vertical-align: top;
            white-space: normal;
            word-wrap: break-word;
            mso-char-indent-count: 1;
            padding-left: 7px;
            background: white;
            mso-pattern: auto;
            font-size: 10pt;
            font-weight: 700;
            font-style: normal;
            font-family: "SimHei", "sans-serif";
            mso-protection: locked visible;
        }

        .x92 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: right;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 9pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x93 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: center;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            font-size: 9pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x94 {
            mso-style-parent: style0;
            mso-number-format: "\#\,\#\#0\.00";
            text-align: right;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 9pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x95 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: left;
            vertical-align: top;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            font-size: 9pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x96 {
            mso-style-parent: style0;
            mso-number-format: "\\ \\ \@";
            text-align: right;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "FangSong", "sans-serif";
            mso-protection: locked visible;
        }

        .x97 {
            mso-style-parent: style0;
            mso-number-format: "\\ \\ \@";
            text-align: right;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "FangSong", "sans-serif";
            mso-protection: locked visible;
        }

        .x98 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: right;
            vertical-align: top;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "FangSong", "sans-serif";
            mso-protection: locked visible;
        }

        .x71 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: center;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            font-size: 13pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x72 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: center;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 11pt;
            font-weight: 400;
            font-style: normal;
            font-family: "Arial", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x73 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: center;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x74 {
            mso-style-parent: style0;
            mso-number-format: "0_ ";
            text-align: center;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x75 {
            mso-style-parent: style0;
            mso-number-format: "0\.0_ ";
            text-align: center;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x76 {
            mso-style-parent: style0;
            mso-number-format: "0\.00_ ";
            text-align: center;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x77 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: left;
            vertical-align: top;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 11pt;
            font-weight: 400;
            font-style: normal;
            font-family: "Arial", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x78 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: left;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x79 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: left;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 11pt;
            font-weight: 400;
            font-style: normal;
            font-family: "Arial", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x80 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: left;
            vertical-align: top;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x81 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: center;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            font-size: 12pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x82 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: center;
            vertical-align: top;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x83 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: center;
            vertical-align: top;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x84 {
            mso-style-parent: style0;
            mso-number-format: "0%";
            text-align: center;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x85 {
            mso-style-parent: style0;
            mso-number-format: "\#\,\#\#0\.00";
            text-align: right;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x86 {
            mso-style-parent: style0;
            mso-number-format: "0\.00_ ";
            text-align: center;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "Arial", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x87 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: center;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x88 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: left;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            font-size: 8pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x89 {
            mso-style-parent: style0;
            mso-number-format: General;
            text-align: center;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 9pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x90 {
            mso-style-parent: style0;
            mso-number-format: "\\ \@";
            text-align: left;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            mso-char-indent-count: 1;
            padding-left: 6px;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 9pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        .x91 {
            mso-style-parent: style0;
            mso-number-format: "0_ ";
            text-align: center;
            vertical-align: middle;
            white-space: normal;
            word-wrap: break-word;
            background: white;
            mso-pattern: auto;
            color: #000000;
            font-size: 9pt;
            font-weight: 400;
            font-style: normal;
            font-family: "SimSun", "sans-serif";
            border-top: 1px solid #000000;
            border-right: 1px solid #000000;
            border-bottom: 1px solid #000000;
            border-left: 1px solid #000000;
            mso-diagonal-down: none;
            mso-diagonal-up: none;
            mso-protection: locked visible;
        }

        -->
    </style>
    <title></title>
</head>
<body link='blue' vlink='purple'>

<table border='0' cellpadding='0' cellspacing='0' style='border-collapse:
 collapse;table-layout:fixed;width:432pt' width='576'>
    <col style='mso-width-source:userset;width:45.75pt' width='61'>
    <col style='mso-width-source:userset;width:82.5pt' width='110'>
    <col style='mso-width-source:userset;width:56.25pt' width='75'>
    <col style='mso-width-source:userset;width:66pt' width='88'>
    <col style='mso-width-source:userset;width:99pt' width='132'>
    <col style='mso-width-source:userset;width:82.5pt' width='110'>
    <tr height='17' id='r0' style='mso-height-source:userset;height:12.75pt'>
        <td class='x69' colspan='6' height='17' style='height:12.75pt;' width='576'>致：xxxxxxx</td>
    </tr>
    <tr height='20' id='r1' style='mso-height-source:userset;height:15.5pt'>
        <td class='x70' colspan='6' height='19' style='height:14.75pt;'>2025年1月-3月结算数据如下，请给予确认，谢谢。</td>
    </tr>
    <tr height='25' id='r2' style='mso-height-source:userset;height:18.75pt'>
        <td class='x71' colspan='6' height='23'
            style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:17.25pt;'>
            英伦名苑二期充电站2025年1月-3月电费计算表
        </td>
    </tr>
    <tr height='28' id='r3' style='mso-height-source:userset;height:21pt'>
        <td class='x73' height='26' style='height:19.5pt;'><span class="font5">序号</span></td>
        <td class='x73' colspan='2' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'><span
                class="font5">时间段</span></td>
        <td class='x73'><span class="font5">电量</span></td>
        <td class='x73'><span class="font5">电价<br></span><span class="font5">(元/千瓦时)</span></td>
        <td class='x73'><span class="font5">电费合计(元)</span></td>
    </tr>
    <tr height='24' id='r4' style='mso-height-source:userset;height:18.5pt'>
        <td class='x74' height='22' style='height:17pt;'>1</td>
        <td class='x75' colspan='2' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'></td>
        <td class='x76'></td>
        <td class='x75'></td>
        <td class='x76'></td>
    </tr>
    <tr height='24' id='r5' style='mso-height-source:userset;height:18pt'>
        <td class='x74' height='22' style='height:16.5pt;'>2</td>
        <td class='x73' colspan='2' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'><span
                class="font5">合计</span></td>
        <td class='x76'></td>
        <td class='x77'></td>
        <td class='x76'></td>
    </tr>
    <tr height='24' id='r6' style='mso-height-source:userset;height:18.5pt'>
        <td class='x78' colspan='6' height='22'
            style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:17pt;'><span
                class="font5">说明：</span></td>
    </tr>
    <tr height='48' id='r7' style='mso-height-source:userset;height:36.25pt'>
        <td class='x80' colspan='6' height='46'
            style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:34.75pt;'>1、电费计算周期：2025年1月-3月<br>2、数据取自电表：上次读数100；本次读数220；倍率40；实用量4800
        </td>
    </tr>
    <tr height='6' id='r8' style='mso-height-source:userset;height:5pt'>
        <td colspan='6' height='6' style='height:5pt;'></td>
    </tr>
    <tr height='45' id='r9' style='mso-height-source:userset;height:33.75pt'>
        <td class='x81' colspan='6' height='43'
            style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:32.25pt;'>
            英伦名苑二期充电站2025年1月-3月效益分成计算表
        </td>
    </tr>
    <tr height='39' id='r10' style='mso-height-source:userset;height:29.5pt'>
        <td class='x73' height='37' style='height:28pt;'><span class="font5">序号</span></td>
        <td class='x73'><span class="font5">结算账单期间</span></td>
        <td class='x82'><span class="font5">期间服务费<br></span><span class="font5">(元)</span></td>
        <td class='x73'><span class="font5">合同比例</span></td>
        <td class='x83'>新能公司80%效益分成<br>(元)</td>
        <td class='x83'>xxxx20%效益分成(元)</td>
    </tr>
    <tr height='29' id='r11' style='mso-height-source:userset;height:22pt'>
        <td class='x74' height='27' style='height:20.5pt;'>1</td>
        <td class='x75'></td>
        <td class='x76'></td>
        <td class='x84'></td>
        <td class='x85'></td>
        <td class='x85'></td>
    </tr>
    <tr height='28' id='r12' style='mso-height-source:userset;height:21.5pt'>
        <td class='x74' height='26' style='height:20pt;'>2</td>
        <td class='x73'><span class="font5">合计</span></td>
        <td class='x86'></td>
        <td class='x87' colspan='2' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>
            xxxx效益分成合计(元)
        </td>
        <td class='x85'></td>
    </tr>
    <tr height='52' id='r13' style='mso-height-source:userset;height:39pt'>
        <td class='x73' height='50' style='height:37.5pt;'><span class="font5">说明</span></td>
        <td class='x88' colspan='5' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>1.
            服务费计算周期：2025年1月-3月<br>2. 服务费根据2025年1月1日-2025年3月31日订单中服务费的总额-巡检订单总服务费
        </td>
    </tr>
    <tr height='6' id='r14' style='mso-height-source:userset;height:5pt'>
        <td height='6' style='height:5pt;'></td>
        <td colspan='5'></td>
    </tr>
    <tr height='25' id='r15' style='mso-height-source:userset;height:19.25pt'>
        <td class='x81' colspan='6' height='23'
            style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:17.75pt;'>英伦名苑二期充电站2025年1月-3月
            应 付 款 统 计 表
        </td>
    </tr>
    <tr height='32' id='r16' style='mso-height-source:userset;height:24pt'>
        <td class='x89' height='30' style='height:22.5pt;'><span class="font9">序号</span></td>
        <td class='x89'><span class="font9">项 目</span></td>
        <td class='x90'><span class="font9">金额<br></span><span class="font9">(元)</span></td>
        <td class='x89' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'><span
                class="font9">备注</span></td>
    </tr>
    <tr height='25' id='r17' style='mso-height-source:userset;height:19pt'>
        <td class='x91' height='23' style='height:17.5pt;'>1</td>
        <td class='x89'><span class="font9">电费应付款</span></td>
        <td class='x92'></td>
        <td class='x93' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'></td>
    </tr>
    <tr height='26' id='r18' style='mso-height-source:userset;height:19.5pt'>
        <td class='x91' height='24' style='height:18pt;'>2</td>
        <td class='x89'><span class="font9">效益分成应付款</span></td>
        <td class='x92'></td>
        <td class='x93' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>
            按合同比例计算服务费分成
        </td>
    </tr>
    <tr height='25' id='r19' style='mso-height-source:userset;height:19pt'>
        <td class='x91' height='23' style='height:17.5pt;'>3</td>
        <td class='x89'><span class="font9">期间应付款合计</span></td>
        <td class='x94'></td>
        <td class='x89' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'>
            期间合计应付款项
        </td>
    </tr>
    <tr height='26' id='r20' style='mso-height-source:userset;height:19.5pt'>
        <td class='x91' height='24' style='height:18pt;'>4</td>
        <td class='x89'><span class="font9">本期应付款</span></td>
        <td class='x94'></td>
        <td class='x77' colspan='3' style='border-right:1px solid #000000;border-bottom:1px solid #000000;'></td>
    </tr>
    <tr height='65' id='r21' style='mso-height-source:userset;height:49pt'>
        <td class='x95' colspan='6' height='63'
            style='border-right:1px solid #000000;border-bottom:1px solid #000000;height:47.5pt;'>说明：<br>收款账户：xxxxxxxx<br>开户行：中国银行深圳中兴支行<br>账号：754957950278
        </td>
    </tr>
    <tr height='18' id='r22' style='mso-height-source:userset;height:13.5pt'>
        <td class='x96' colspan='6' height='17' style='height:12.75pt;'><span style='float:right'><span
                style='mso-spacerun:yes;'>&nbsp; </span>深圳新能智慧充电科技有限公司</span></td>
    </tr>
    <tr height='18' id='r23' style='mso-height-source:userset;height:13.5pt'>
        <td colspan='4' height='18' style='height:13.5pt;'></td>
        <td></td>
        <td class='x98'><span style='float:right'>日期：2025.4.17</span></td>
    </tr>
</table>
</body>

</html>
