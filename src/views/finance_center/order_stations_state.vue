<!--suppress JSUnresolvedReference, JSValidateTypes -->
<template>
  <div class="Equipment">
    <!-- 面包屑 -->
    <div class="crumb">
      <Crumb ref="crumb"></Crumb>
    </div>
    <div class="form" v-loading="div_loading">
      <!-- 搜索栏 -->
      <div class="search text-c">
        <el-row>
          <el-col :span="24" class="mt-10">
            <span class="select-box ml-5">
              场站名称:
              <el-input
                  v-model="query_form.station_name"
                  style="width: 200px;"
                  size="small"
                  placeholder="请输入场站名称"
                  clearable
              ></el-input>
            </span>

            <span class="select-box ml-5" v-if="pid <= 0">
              运营商:
              <el-select
                  v-model="query_form.corp_id"
                  filterable
                  style="width: 150px;"
                  size="small"
                  placeholder="运营商"
                  clearable
              >
                <el-option label="全部" :value="0"></el-option>
                <el-option
                    v-for="(v,k) in corp_list_all"
                    :key="'查询表单运营商'+k"
                    :label="v.name"
                    :value="v.id"
                ></el-option>
              </el-select>
            </span>

            <el-button @click="getTableData()" size="small" class="duck-green-button ml-5">查询</el-button>
            <el-button @click="reset_query_form(true)" type="info" size="small" class="ml-5">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 操作栏 -->
      <div>
        <el-card
            class="mt-10 mb-10 text-c"
            shadow="always"
            :body-style="{ padding: '8px' }"
            style="background: #f5fafe;"
        >
          <div class="cl">
            <el-button @click="exportExcel()" size="small" type="success" class="f-l" :disabled="selection_table_data.length === 0">
              <i class="el-icon-download"></i>
              导出Excel
              <span v-if="selection_table_data.length > 0">({{selection_table_data.length}})</span>
            </el-button>

            <!-- 调试信息 -->
            <el-tag v-if="selection_table_data.length > 0" type="info" class="f-l ml-5">
              已选中: {{selection_table_data.length}} 条
            </el-tag>

            <el-tag type="info" class="f-r" effect="dark">共：{{query_form.total}} 条</el-tag>
            <span class="f-r mr-5">
              每页:
              <el-select
                  v-model="query_form.limit"
                  size="small"
                  class="width-120"
                  @change="getTableData()"
              >
                <el-option
                    v-for="v in query_form_limit"
                    :key="'查询表单'+v"
                    :label="v.toString()"
                    :value="v"
                ></el-option>
              </el-select>条
            </span>
          </div>
        </el-card>
      </div>

      <!-- 大表格 -->
      <div class="Table_Form">
        <el-table
            style="width:100%"
            :data="table_data"
            @selection-change="handleSelectionChange"
            border
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
          <el-table-column prop="station_name" label="场站名称" min-width="150" align="center"></el-table-column>
          <el-table-column prop="electricity_total" label="电量(kWh)" width="120" align="center">
            <template #default="{row}">
              <div>{{(row.electricity_total/10000).toFixed(2)}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="electricity_price" label="电价(元)" width="120" align="center">
            <template #default="{row}">
              <div style="color: #ff0000">{{(row.electricity_price/10000).toFixed(2)}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="service_price" label="服务费(元)" width="120" align="center">
            <template #default="{row}">
              <div style="color: #ff0000">{{(row.service_price/10000).toFixed(4)}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="range_time" label="统计时间" min-width="150" align="center"></el-table-column>
          <el-table-column
              prop="img"
              label="电费单"
              min-width="180"
              align="center"
          >
            <template #default="{row}">
              <el-tooltip
                  class="item"
                  effect="light"
                  v-if="row.img"
                  placement="top"
              >
                <div slot="content" style="text-align:center;min-width:150px;">
                  <img :src="row.img" style="width:150px;" />
                </div>
                <div v-if="row.img" @click="showLargeImage(row.img)" style="cursor: pointer;">
                  <img :src="row.img" width="30" height="30" />
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="ratio_ser_price" label="服务费分成占比" width="140" align="center">
            <template #default="{row}">
              <div>{{row.ratio_ser_price}}%</div>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" min-width="160" align="center"></el-table-column>
          <el-table-column
              class="operateForm"
              prop="operate"
              label="操作"
              min-width="200"
              align="center"
              fixed="right"
          >
            <template #default="{row}">
              <div>
                <el-button
                    class="duck-green-button"
                    @click="handlePreview(row)"
                    style="border:none;padding: 5px 10px;margin-right:5px"
                    size="mini"
                >预览</el-button>
                <el-button
                    class="duck-yellow-button"
                    @click="handleEdit(row)"
                    style="border:none;padding: 5px 10px;margin-right:5px"
                    size="mini"
                >编辑</el-button>
                <el-button
                    class="duck-red-button"
                    @click="handleDelete(row)"
                    style="border:none;padding: 5px 10px;"
                    size="mini"
                >删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <pagination
          :page-size="query_form.limit"
          :total="query_form.total"
          :currentpage="query_form.page"
          @page-change="table_tiaozhuan"
      ></pagination>

      <!-- 编辑对话框 -->
      <el-dialog
          title="编辑场站状态"
          :visible.sync="edit_dialog_visible"
          width="600px"
          :before-close="closeEditDialog"
      >
        <el-form :model="edit_form" :rules="edit_rules" ref="editForm" label-width="130px">
          <el-form-item label="场站名称" prop="station_name">
            <el-input v-model="edit_form.station_name" placeholder="请输入场站名称"></el-input>
          </el-form-item>
          <el-form-item label="电量(kWh)" prop="electricity_total">
            <el-input-number
                v-model="edit_form.electricity_total"
                :precision="4"
                :step="0.01"
                :min="0"
                style="width: 100%"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="电价(元)" prop="electricity_price">
            <el-input-number
                v-model="edit_form.electricity_price"
                :precision="2"
                :step="0.01"
                :min="0"
                style="width: 100%"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="服务费(元)" prop="service_price">
            <el-input-number
                v-model="edit_form.service_price"
                :precision="4"
                :step="0.0001"
                :min="0"
                style="width: 100%"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="统计时间" prop="range_time">
            <el-input v-model="edit_form.range_time" disabled placeholder="如：2025年1月-3月"></el-input>
          </el-form-item>
          <el-form-item label="服务费分成占比" prop="ratio_ser_price">
            <el-input-number
                v-model="edit_form.ratio_ser_price"
                :precision="0"
                :step="1"
                :min="0"
                :max="100"
                style="width: 100%"
            ></el-input-number>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="closeEditDialog">取 消</el-button>
          <el-button type="primary" @click="submitEdit" :loading="edit_loading">确 定</el-button>
        </div>
      </el-dialog>

      <!-- Excel预览对话框 -->
      <el-dialog
          title="Excel预览"
          :visible.sync="preview_dialog_visible"
          width="50%"
          :before-close="closePreviewDialog"
          top="5vh"
      >
        <div style="max-height: 80vh; overflow-y: auto;">
          <ExcelPreview :station-data="preview_station_data" />
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="closePreviewDialog">关 闭</el-button>
          <el-button type="primary" @click="exportCurrentPreview" :loading="export_loading">导出Excel</el-button>
        </div>
      </el-dialog>

      <!-- 大图预览弹框 -->
      <el-dialog
          title="电费单"
          :visible.sync="imageDialogVisible"
          width="50%"
          center
      >
        <div style="text-align: center;">
          <img :src="largeImageUrl" style="max-width: 100%;" />
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { queryFormLimit } from "@/common.js";
import pagination from "@/components/pagination.vue";
import {
  GetCorpListAllApi,
  OrderStationsStateListApi,
  OrderStationsStateUpdateApi,
  OrderStationsStateDeleteApi,
  OrderStationsStateExportApi
} from "@/request/api";
import Crumb from "@/views/home/<USER>";
import ExcelPreview from "@/components/ExcelPreview.vue";

export default {
  components: {
    pagination,
    Crumb,
    ExcelPreview
  },
  data() {
    return {
      div_loading: true,

      query_form: {},
      y_query_form: {
        station_name: "",
        corp_id: "",
        page: 1,
        limit: 10
      },
      pid: null,
      query_form_limit: queryFormLimit,
      table_data: [],

      corp_list_all: [],

      // 多选相关
      selection_table_data: [],

      // 编辑对话框相关
      edit_dialog_visible: false,
      edit_loading: false,
      edit_form: {
        id: null,
        station_name: "",
        electricity_total: 0,
        electricity_price: 0,
        service_price: 0,
        range_time: "",
        ratio_ser_price: 80
      },
      edit_rules: {
        station_name: [
          { required: true, message: '请输入场站名称', trigger: 'blur' }
        ],
        electricity_total: [
          { required: true, message: '请输入电量', trigger: 'blur' }
        ],
        electricity_price: [
          { required: true, message: '请输入电价', trigger: 'blur' }
        ],
        service_price: [
          { required: true, message: '请输入服务费', trigger: 'blur' }
        ],
        range_time: [
          { required: true, message: '请输入统计时间', trigger: 'blur' }
        ],
        ratio_ser_price: [
          { required: true, message: '请输入服务费分成占比', trigger: 'blur' }
        ]
      },

      // 预览对话框相关
      preview_dialog_visible: false,
      preview_station_data: {},
      export_loading: false,

      // 大图预览
      imageDialogVisible: false,
      largeImageUrl: ""
    };
  },
  created() {
    this.pid = parseInt(localStorage.getItem("pid"));
    this.reset_query_form(true);
    this.getCorpListAll();
  },
  methods: {
    // 显示大图
    showLargeImage(url) {
      this.largeImageUrl = url;
      this.imageDialogVisible = true;
    },
    // 获取运营商列表
    async getCorpListAll() {
      let res = await GetCorpListAllApi();
      this.commonFunc.show_log("GetCorpListAllApi-res", res);
      if (!res) return;
      this.corp_list_all = res.data;
    },

    // 重置查询表单
    reset_query_form(e = false) {
      this.query_form = Object.assign({}, this.y_query_form);
      if (e) {
        this.getTableData(1);
      }
    },

    // 分页跳转
    table_tiaozhuan(val) {
      this.div_loading = true;
      this.getTableData(val);
    },

    // 获取表格数据
    async getTableData(page = 1) {
      this.div_loading = true;
      this.$set(this.query_form, "page", page);
      let form = this.query_form;

      let res = await OrderStationsStateListApi(form);
      this.commonFunc.show_log("OrderStationsStateListApi-res", res);
      if (!res) {
        this.div_loading = false;
        return;
      }
      this.table_data = res.data.data;
      this.query_form.total = res.data.total;
      this.div_loading = false;
    },

    // 多选处理
    handleSelectionChange(selection) {
      this.selection_table_data = selection;
      this.commonFunc.show_log("handleSelectionChange-selection_table_data", this.selection_table_data);
    },

    // 导出Excel
    async exportExcel() {
      if (this.selection_table_data.length === 0) {
        this.$message.warning("请先选择要导出的数据");
        return;
      }

      // 显示确认对话框
      try {
        await this.$confirm(`确定要导出选中的 ${this.selection_table_data.length} 条记录吗？`, '确认导出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });
      } catch {
        this.$message.info('已取消导出');
        return;
      }

      // 显示导出进度
      const loading = this.$loading({
        lock: true,
        text: '正在导出数据，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      const exportResults = [];
      let successCount = 0;
      let failCount = 0;

      try {
        // 循环调用导出接口
        for (let i = 0; i < this.selection_table_data.length; i++) {
          const item = this.selection_table_data[i];

          try {
            const res = await OrderStationsStateExportApi({ id: item.id });
            this.commonFunc.show_log(`OrderStationsStateExportApi-res-${item.id}`, res);

            if (res && res.code === 200 && res.data) {
              exportResults.push({
                id: item.id,
                station_name: item.station_name,
                success: true,
                url: res.data.url,
                title: res.data.title
              });
              successCount++;
            } else {
              exportResults.push({
                id: item.id,
                station_name: item.station_name,
                success: false,
                error: res?.msg || '导出失败'
              });
              failCount++;
            }
          } catch (error) {
            this.commonFunc.show_log(`OrderStationsStateExportApi-error-${item.id}`, error);
            exportResults.push({
              id: item.id,
              station_name: item.station_name,
              success: false,
              error: error.message || '网络错误'
            });
            failCount++;
          }
        }

        // 显示导出结果
        this.showExportResults(exportResults, successCount, failCount);

      } catch (error) {
        this.commonFunc.show_log("exportExcel-error", error);
        this.$message.error("导出过程中发生错误");
      } finally {
        loading.close();
      }
    },

    // 显示导出结果
    showExportResults(results, successCount, failCount) {
      const successResults = results.filter(r => r.success);
      const failResults = results.filter(r => !r.success);

      let message = `导出完成！<span style="color:#67C23A">成功：${successCount} 条</span>，<span style="color:#F56C6C">失败：${failCount} 条</span><br><br>`;

      if (successCount > 0) {
        message += '成功导出的文件：<br>';
        successResults.forEach(result => {
          message += `<br>• ${result.title}<br>`;
        });
      }

      if (failCount > 0) {
        message += '<br>导出失败的记录：<br>';
        failResults.forEach(result => {
          message += `• ${result.station_name}: ${result.error}<br>`;
        });
      }

      // 如果有成功的导出，显示下载链接
      if (successCount > 0) {
        this.$alert(message, '导出结果', {
          width: '800px',
          confirmButtonText: '下载',
          type: failCount > 0 ? 'warning' : 'success',
          dangerouslyUseHTMLString: true
        }).then(() => {
          // 自动下载所有成功的文件
          this.downloadAllFiles(successResults);
        });
      } else {
        this.$alert(message, '导出失败', {
          confirmButtonText: '确定',
          type: 'error',
          dangerouslyUseHTMLString: true
        });
      }
    },

    // 下载所有成功的文件
    downloadAllFiles(successResults) {
      if (successResults.length === 0) return;

      // 如果只有一个文件，直接下载
      if (successResults.length === 1) {
        window.open(successResults[0].url, '_blank');
        return;
      }

      // 多个文件时，延时下载避免浏览器阻止
      successResults.forEach((result, index) => {
        setTimeout(() => {
          window.open(result.url, '_blank');
          this.commonFunc.show_log(`downloadAllFiles-${index}`, `正在下载: ${result.title}`);
        }, index * 300); // 每个文件间隔500ms下载
      });

      // 提示用户
      this.$message.success(`正在下载 ${successResults.length} 个文件，请注意浏览器下载提示`);
    },

    // 编辑
    handleEdit(row) {
      this.edit_form = {
        id: row.id,
        station_name: row.station_name,
        electricity_total: row.electricity_total / 10000,
        electricity_price: row.electricity_price / 10000,
        service_price: row.service_price / 10000,
        range_time: row.range_time,
        ratio_ser_price: row.ratio_ser_price
      };
      this.edit_dialog_visible = true;
    },

    // 关闭编辑对话框
    closeEditDialog() {
      this.edit_dialog_visible = false;
      this.$refs.editForm.resetFields();
    },

    // 提交编辑
    async submitEdit() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          this.edit_loading = true;

          // 转换数据格式
          const params = {
            id: this.edit_form.id,
            station_name: this.edit_form.station_name,
            electricity_total: Math.round(this.edit_form.electricity_total * 10000), // 保留2位小数
            electricity_price: Math.round(this.edit_form.electricity_price * 10000), // 保留4位小数
            service_price: Math.round(this.edit_form.service_price * 10000), // 保留4位小数
            range_time: this.edit_form.range_time,
            ratio_ser_price: this.edit_form.ratio_ser_price
          };

          try {
            const res = await OrderStationsStateUpdateApi(params);
            this.commonFunc.show_log("OrderStationsStateUpdateApi-res", res);

            if (!res || res.code !== 200) {
              this.$message.error("更新失败");
              return;
            }

            this.$message.success("更新成功");
            this.closeEditDialog();
            this.getTableData();
          } catch (error) {
            this.commonFunc.show_log("OrderStationsStateUpdateApi-error", error);
            this.$message.error("更新时发生错误");
          } finally {
            this.edit_loading = false;
          }
        }
      });
    },

    // 删除
    handleDelete(row) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await OrderStationsStateDeleteApi({ id: row.id });
          this.commonFunc.show_log("OrderStationsStateDeleteApi-res", res);

          if (!res) {
            this.$message.error("删除失败");
            return;
          }

          this.$message.success("删除成功");
          this.getTableData();
        } catch (error) {
          this.commonFunc.show_log("OrderStationsStateDeleteApi-error", error);
          this.$message.error("删除时发生错误");
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 预览Excel
    handlePreview(row) {
      this.preview_station_data = {
        station_name: row.station_name,
        electricity_total: row.electricity_total,
        electricity_price: row.electricity_price,
        service_price: row.service_price,
        range_time: row.range_time,
        ratio_ser_price: row.ratio_ser_price
      };
      this.preview_dialog_visible = true;
    },

    // 关闭预览对话框
    closePreviewDialog() {
      this.preview_dialog_visible = false;
      this.preview_station_data = {};
    },

    // 导出当前预览的Excel
    async exportCurrentPreview() {
      if (!this.preview_station_data.station_name) {
        this.$message.warning("没有可导出的数据");
        return;
      }

      this.export_loading = true;

      try {
        // 找到当前预览数据对应的原始行数据
        const originalRow = this.table_data.find(item =>
          item.station_name === this.preview_station_data.station_name &&
          item.range_time === this.preview_station_data.range_time
        );

        if (!originalRow) {
          this.$message.error("找不到对应的数据记录");
          return;
        }

        const res = await OrderStationsStateExportApi({ id: originalRow.id });
        this.commonFunc.show_log(`OrderStationsStateExportApi-res-${originalRow.id}`, res);

        if (res && res.code === 200 && res.data) {
          // 直接下载文件
          window.open(res.data.url, '_blank');
          this.$message.success(`导出成功：${res.data.title}`);
        } else {
          this.$message.error(res?.msg || '导出失败');
        }
      } catch (error) {
        this.commonFunc.show_log("exportCurrentPreview-error", error);
        this.$message.error("导出时发生错误");
      } finally {
        this.export_loading = false;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.Equipment {
  height: 100%;

  .crumb {
  }

  .form {
    box-shadow: 3px 0 5px #ccc;

    // 表格
    .Table_Form {
      margin-left: 10px;
      .ellipsis-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .ellipsis-id {
        white-space: nowrap;
      }
      .el-input-number--small {
        width: 50px;
      }
    }
  }
}

// 全局样式覆盖
::v-deep .el-table .cell {
  text-align: center;
}

::v-deep .el-pagination {
  text-align: center;
}

::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #009688;
}

// 按钮样式
.duck-green-button {
  background-color: #009688;
  border-color: #009688;
  color: white;
}

.duck-yellow-button {
  background-color: #f39c12;
  border-color: #f39c12;
  color: white;
}

.duck-red-button {
  background-color: #e74c3c;
  border-color: #e74c3c;
  color: white;
}

// 选择框样式
.select-box {
  display: inline-block;
  margin-right: 10px;
  vertical-align: middle;
}

// 工具类
.text-c {
  text-align: center;
}

.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.ml-5 {
  margin-left: 5px;
}

.mr-5 {
  margin-right: 5px;
}

.f-l {
  float: left;
}

.f-r {
  float: right;
}

.cl::after {
  content: "";
  display: block;
  clear: both;
}

.width-120 {
  width: 120px;
}
</style>
